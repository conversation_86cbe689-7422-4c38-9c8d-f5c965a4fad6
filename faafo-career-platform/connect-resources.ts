import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function connectResourcesToCareerPaths() {
  console.log('🔗 Connecting learning resources to career paths...');

  try {
    // Get all career paths that actually exist in our database
    const cybersecurityPath = await prisma.careerPath.findUnique({
      where: { slug: 'cybersecurity-specialist' }
    });
    const dataSciencePath = await prisma.careerPath.findUnique({
      where: { slug: 'data-scientist' }
    });
    const digitalMarketingPath = await prisma.careerPath.findUnique({
      where: { slug: 'digital-marketing-specialist' }
    });
    const uxUiDesignerPath = await prisma.careerPath.findUnique({
      where: { slug: 'ux-ui-designer' }
    });
    const aiMlEngineerPath = await prisma.careerPath.findUnique({
      where: { slug: 'ai-machine-learning-engineer' }
    });
    const freelanceDevPath = await prisma.careerPath.findUnique({
      where: { slug: 'freelance-web-developer' }
    });
    const onlineBusinessPath = await prisma.careerPath.findUnique({
      where: { slug: 'simple-online-business' }
    });

    // Log which paths were found
    console.log('Found career paths:', {
      cybersecurity: !!cybersecurityPath,
      dataScience: !!dataSciencePath,
      digitalMarketing: !!digitalMarketingPath,
      uxUi: !!uxUiDesignerPath,
      aiMl: !!aiMlEngineerPath,
      freelanceDev: !!freelanceDevPath,
      onlineBusiness: !!onlineBusinessPath
    });

    if (!cybersecurityPath || !dataSciencePath || !digitalMarketingPath ||
        !uxUiDesignerPath || !aiMlEngineerPath || !freelanceDevPath || !onlineBusinessPath) {
      throw new Error('Some required career paths not found');
    }

    // Note: We'll connect resources to career paths without clearing existing connections

    // Connect cybersecurity resources
    const cybersecurityResources = await prisma.learningResource.findMany({
      where: { category: 'CYBERSECURITY' }
    });

    console.log(`Connecting ${cybersecurityResources.length} cybersecurity resources...`);
    for (const resource of cybersecurityResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: cybersecurityPath.id }
          }
        }
      });
    }

    // Connect data science resources
    const dataScienceResources = await prisma.learningResource.findMany({
      where: { category: 'DATA_SCIENCE' }
    });

    console.log(`Connecting ${dataScienceResources.length} data science resources...`);
    for (const resource of dataScienceResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: dataSciencePath.id }
          }
        }
      });
    }

    // Connect digital marketing resources
    const digitalMarketingResources = await prisma.learningResource.findMany({
      where: { category: 'DIGITAL_MARKETING' }
    });

    console.log(`Connecting ${digitalMarketingResources.length} digital marketing resources...`);
    for (const resource of digitalMarketingResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: digitalMarketingPath.id }
          }
        }
      });
    }

    // Connect AI resources to both data science and AI/ML engineer paths
    const aiResources = await prisma.learningResource.findMany({
      where: { category: 'ARTIFICIAL_INTELLIGENCE' }
    });

    console.log(`Connecting ${aiResources.length} AI resources...`);
    for (const resource of aiResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: [
              { id: dataSciencePath.id },
              { id: aiMlEngineerPath.id }
            ]
          }
        }
      });
    }

    // Connect UX/UI Design resources
    const uxUiResources = await prisma.learningResource.findMany({
      where: { category: 'UX_UI_DESIGN' }
    });

    console.log(`Connecting ${uxUiResources.length} UX/UI resources...`);
    for (const resource of uxUiResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: uxUiDesignerPath.id }
          }
        }
      });
    }

    // Connect Web Development resources to freelance dev path
    const webDevResources = await prisma.learningResource.findMany({
      where: { category: 'WEB_DEVELOPMENT' }
    });

    console.log(`Connecting ${webDevResources.length} web development resources...`);
    for (const resource of webDevResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: [
              { id: freelanceDevPath.id },
              { id: uxUiDesignerPath.id }
            ]
          }
        }
      });
    }

    console.log('✅ Successfully connected learning resources to career paths');
  } catch (error) {
    console.error('❌ Error connecting resources to career paths:', error);
    throw error;
  }
}

async function main() {
  await connectResourcesToCareerPaths();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
