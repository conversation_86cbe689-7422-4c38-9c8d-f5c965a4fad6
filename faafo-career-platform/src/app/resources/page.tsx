'use client';

import React, { useState, useMemo, useEffect } from 'react';
import Link from 'next/link';
import { ExternalLink, Heart, Brain, DollarSign, Users, BookOpen, Video, Headphones, Search, Filter, Code, Shield, BarChart, Blocks, Megaphone, Cpu, Star, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import PageLayout from '@/components/layout/PageLayout';
import { getAllStaticResources, getMindsetResources, getLearningResources } from '@/lib/staticResources';

interface Resource {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  author?: string;
  duration?: string;
  skillLevel?: string;
  cost?: string;
  averageRating?: number;
  totalRatings?: number;
  careerPaths?: {
    id: string;
    name: string;
    slug: string;
  }[];
}

// Import static resources from centralized location
const mindsetResources: Resource[] = getMindsetResources();
const learningResources: Resource[] = getLearningResources();

// Static resources are now imported from centralized location
const allResources = [...mindsetResources, ...learningResources];

const categoryInfo = {
  // Mindset Categories
  fear: {
    title: 'Overcoming Fear & Anxiety',
    icon: Heart,
    description: 'Resources to help you manage fear, anxiety, and uncertainty during career transitions.',
    color: 'text-red-600 dark:text-red-400'
  },
  financial: {
    title: 'Financial Planning',
    icon: DollarSign,
    description: 'Practical advice for managing finances and building security during career changes.',
    color: 'text-green-600 dark:text-green-400'
  },
  imposter: {
    title: 'Imposter Syndrome',
    icon: Brain,
    description: 'Strategies to overcome self-doubt and build confidence in your abilities.',
    color: 'text-purple-600 dark:text-purple-400'
  },
  motivation: {
    title: 'Motivation & Mindset',
    icon: Users,
    description: 'Inspiration and practical advice to maintain motivation throughout your journey.',
    color: 'text-gray-600 dark:text-gray-400'
  },
  planning: {
    title: 'Strategic Planning',
    icon: BookOpen,
    description: 'Frameworks and tools for planning and executing your career transition.',
    color: 'text-orange-600 dark:text-orange-400'
  },
  // Skill Development Categories
  cybersecurity: {
    title: 'Cybersecurity',
    icon: Shield,
    description: 'Learn ethical hacking, network security, and digital forensics skills.',
    color: 'text-red-600 dark:text-red-400'
  },
  'data-science': {
    title: 'Data Science',
    icon: BarChart,
    description: 'Master machine learning, data analysis, and statistical modeling.',
    color: 'text-gray-600 dark:text-gray-400'
  },
  blockchain: {
    title: 'Blockchain',
    icon: Blocks,
    description: 'Understand blockchain technology, smart contracts, and cryptocurrency.',
    color: 'text-yellow-600 dark:text-yellow-400'
  },
  'project-management': {
    title: 'Project Management',
    icon: Users,
    description: 'Learn agile methodologies, team leadership, and project planning.',
    color: 'text-green-600 dark:text-green-400'
  },
  'digital-marketing': {
    title: 'Digital Marketing',
    icon: Megaphone,
    description: 'Master SEO, social media marketing, and content strategy.',
    color: 'text-pink-600 dark:text-pink-400'
  },
  ai: {
    title: 'Artificial Intelligence',
    icon: Cpu,
    description: 'Explore AI concepts, machine learning, and neural networks.',
    color: 'text-purple-600 dark:text-purple-400'
  },
  'web-development': {
    title: 'Web Development',
    icon: Code,
    description: 'Learn frontend and backend development skills.',
    color: 'text-indigo-600 dark:text-indigo-400'
  },
  'mobile-development': {
    title: 'Mobile Development',
    icon: Code,
    description: 'Build mobile applications for iOS and Android.',
    color: 'text-teal-600 dark:text-teal-400'
  },
  'cloud-computing': {
    title: 'Cloud Computing',
    icon: Shield,
    description: 'Learn cloud platforms and distributed systems.',
    color: 'text-sky-600 dark:text-sky-400'
  },
  'language-learning': {
    title: 'Language Learning',
    icon: BookOpen,
    description: 'Learn new languages for global opportunities.',
    color: 'text-rose-600 dark:text-rose-400'
  },
  'entrepreneurship': {
    title: 'Entrepreneurship',
    icon: Users,
    description: 'Start and grow your own business.',
    color: 'text-amber-600 dark:text-amber-400'
  }
};

const getResourceIcon = (type: Resource['type']) => {
  switch (type) {
    case 'article':
      return BookOpen;
    case 'video':
      return Video;
    case 'podcast':
      return Headphones;
    case 'book':
      return BookOpen;
    case 'course':
      return BookOpen;
    case 'certification':
      return Shield;
    case 'tutorial':
      return Code;
    default:
      return BookOpen;
  }
};

export default function ResourcesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedResourceType, setSelectedResourceType] = useState<string>('all'); // 'mindset' or 'learning' or 'all'
  const [selectedSkillLevel, setSelectedSkillLevel] = useState<string>('all');
  const [selectedCost, setSelectedCost] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('relevance'); // 'relevance', 'rating', 'title', 'newest'
  const [databaseResources, setDatabaseResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const categories = Object.keys(categoryInfo) as Array<keyof typeof categoryInfo>;
  const types = ['all', 'article', 'video', 'podcast', 'book', 'course', 'certification', 'tutorial'];
  const skillLevels = ['all', 'beginner', 'intermediate', 'advanced'];
  const costOptions = ['all', 'free', 'freemium', 'paid'];
  const sortOptions = [
    { value: 'relevance', label: 'Relevance' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'title', label: 'Title A-Z' },
    { value: 'newest', label: 'Newest First' },
  ];

  // Fetch database resources
  useEffect(() => {
    fetchDatabaseResources();
  }, []);

  const fetchDatabaseResources = async () => {
    try {
      setLoading(true);
      // Request all resources by setting a high limit
      const response = await fetch('/api/learning-resources?limit=1000');

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setDatabaseResources(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching database resources:', error);
      setError('Failed to load resources from database');
    } finally {
      setLoading(false);
    }
  };

  const { filteredResources, totalAvailableResources } = useMemo(() => {
    // Combine static and database resources
    let resourcesToFilter = [...allResources, ...databaseResources];

    // Remove duplicates based on URL
    const uniqueResources = resourcesToFilter.reduce((acc, resource) => {
      const existing = acc.find(r => r.url === resource.url);
      if (!existing) {
        acc.push(resource);
      }
      return acc;
    }, [] as Resource[]);

    // Filter by resource type (mindset vs learning)
    if (selectedResourceType === 'mindset') {
      resourcesToFilter = mindsetResources; // Keep static mindset resources
    } else if (selectedResourceType === 'learning') {
      // Combine static learning resources with database resources for skill development
      const combinedLearningResources = [...learningResources, ...databaseResources];
      // Remove duplicates based on URL
      resourcesToFilter = combinedLearningResources.reduce((acc, resource) => {
        const existing = acc.find(r => r.url === resource.url);
        if (!existing) {
          acc.push(resource);
        }
        return acc;
      }, [] as Resource[]);
    } else {
      resourcesToFilter = uniqueResources; // Use combined unique resources
    }

    const totalAvailable = resourcesToFilter.length;

    // Apply filters
    const filtered = resourcesToFilter.filter(resource => {
      const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           resource.author?.toLowerCase().includes(searchTerm.toLowerCase());

      // Handle category matching for both static and database resources
      let matchesCategory = selectedCategory === 'all';
      if (!matchesCategory) {
        // For static resources, use direct comparison
        if (resource.category === selectedCategory) {
          matchesCategory = true;
        }
        // For database resources, handle enum values
        const categoryMap: { [key: string]: string } = {
          'cybersecurity': 'CYBERSECURITY',
          'data-science': 'DATA_SCIENCE',
          'blockchain': 'BLOCKCHAIN',
          'project-management': 'PROJECT_MANAGEMENT',
          'digital-marketing': 'DIGITAL_MARKETING',
          'ai': 'ARTIFICIAL_INTELLIGENCE',
          'web-development': 'WEB_DEVELOPMENT',
          'mobile-development': 'MOBILE_DEVELOPMENT',
          'cloud-computing': 'CLOUD_COMPUTING',
          'financial': 'FINANCIAL_LITERACY',
          'language-learning': 'LANGUAGE_LEARNING',
          'entrepreneurship': 'ENTREPRENEURSHIP'
        };
        if (resource.category === categoryMap[selectedCategory]) {
          matchesCategory = true;
        }
      }

      const matchesType = selectedType === 'all' ||
                         resource.type?.toLowerCase() === selectedType ||
                         resource.type === selectedType.toUpperCase();

      const matchesSkillLevel = selectedSkillLevel === 'all' ||
                               resource.skillLevel?.toLowerCase() === selectedSkillLevel ||
                               resource.skillLevel === selectedSkillLevel.toUpperCase();

      const matchesCost = selectedCost === 'all' ||
                         resource.cost?.toLowerCase() === selectedCost ||
                         resource.cost === selectedCost.toUpperCase();

      return matchesSearch && matchesCategory && matchesType && matchesSkillLevel && matchesCost;
    });

    // Apply sorting
    switch (sortBy) {
      case 'title':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'rating':
        filtered.sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0));
        break;
      case 'newest':
        // For database resources, this would use createdAt; for static, maintain order
        break;
      case 'relevance':
      default:
        // Keep original order for relevance
        break;
    }

    return { filteredResources: filtered, totalAvailableResources: totalAvailable };
  }, [searchTerm, selectedCategory, selectedType, selectedResourceType, selectedSkillLevel, selectedCost, sortBy, databaseResources]);

  if (loading) {
    return (
      <PageLayout maxWidth="6xl" padding="lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading resources...</p>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout maxWidth="6xl" padding="lg">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Career Development Resources</h1>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          Comprehensive resources to support your career transition journey. From mindset and emotional support
          to technical skill development across high-demand fields like cybersecurity, data science, AI, and more.
        </p>
        {error && (
          <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-yellow-800 dark:text-yellow-200 text-sm">
              {error} - Showing static resources only.
            </p>
          </div>
        )}
      </div>

      {/* Resource Type Tabs */}
      <div className="mb-8 flex justify-center">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm">
          <div className="flex gap-1">
            <button
              onClick={() => setSelectedResourceType('all')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedResourceType === 'all'
                  ? 'bg-gray-800 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              All Resources
            </button>
            <button
              onClick={() => setSelectedResourceType('mindset')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedResourceType === 'mindset'
                  ? 'bg-gray-800 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              Mindset & Support
            </button>
            <button
              onClick={() => setSelectedResourceType('learning')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedResourceType === 'learning'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              Skill Development
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex flex-col gap-4">
          {/* Search Bar */}
          <div className="relative">
            <label htmlFor="resource-search" className="sr-only">
              Search resources by title, description, or author
            </label>
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" aria-hidden="true" />
            <input
              id="resource-search"
              type="text"
              placeholder="Search resources by title, description, or author..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              aria-label="Search resources"
            />
          </div>

          {/* Filters Row */}
          <div className="flex flex-wrap gap-3 items-center">
            <Filter className="text-gray-400 h-5 w-5" />

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {categoryInfo[category].title}
                </option>
              ))}
            </select>

            {/* Type Filter */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="all">All Types</option>
              {types.slice(1).map(type => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}s
                </option>
              ))}
            </select>

            {/* Skill Level Filter */}
            <select
              value={selectedSkillLevel}
              onChange={(e) => setSelectedSkillLevel(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="all">All Levels</option>
              {skillLevels.slice(1).map(level => (
                <option key={level} value={level}>
                  {level.charAt(0).toUpperCase() + level.slice(1)}
                </option>
              ))}
            </select>

            {/* Cost Filter */}
            <select
              value={selectedCost}
              onChange={(e) => setSelectedCost(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="all">All Costs</option>
              {costOptions.slice(1).map(cost => (
                <option key={cost} value={cost}>
                  {cost.charAt(0).toUpperCase() + cost.slice(1)}
                </option>
              ))}
            </select>

            {/* Sort Filter */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  Sort by {option.label}
                </option>
              ))}
            </select>

            {/* Clear Filters Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setSelectedType('all');
                setSelectedSkillLevel('all');
                setSelectedCost('all');
                setSortBy('relevance');
              }}
              className="text-sm"
            >
              Clear Filters
            </Button>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
          Showing {filteredResources.length} of {totalAvailableResources} resources
          {selectedResourceType !== 'all' && (
            <span className="ml-2 text-blue-600 dark:text-blue-400">
              ({selectedResourceType === 'mindset' ? 'Mindset & Support' : 'Skill Development'} only)
            </span>
          )}
        </div>
      </div>

      {/* Resources Display */}
      {filteredResources.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400 text-lg">
            No resources found matching your criteria. Try adjusting your search or filters.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredResources.map((resource) => {
            const ResourceIcon = getResourceIcon(resource.type);

            // Map database category names to categoryInfo keys
            const getCategoryKey = (category: string) => {
              const categoryMap: { [key: string]: string } = {
                'CYBERSECURITY': 'cybersecurity',
                'DATA_SCIENCE': 'data-science',
                'BLOCKCHAIN': 'blockchain',
                'PROJECT_MANAGEMENT': 'project-management',
                'DIGITAL_MARKETING': 'digital-marketing',
                'ARTIFICIAL_INTELLIGENCE': 'ai',
                'WEB_DEVELOPMENT': 'web-development',
                'MOBILE_DEVELOPMENT': 'mobile-development',
                'CLOUD_COMPUTING': 'cloud-computing',
                'FINANCIAL_LITERACY': 'financial',
                'LANGUAGE_LEARNING': 'language-learning',
                'ENTREPRENEURSHIP': 'entrepreneurship'
              };
              return categoryMap[category] || category.toLowerCase().replace(/_/g, '-');
            };

            const categoryKey = getCategoryKey(resource.category);
            const category = categoryInfo[categoryKey as keyof typeof categoryInfo];

            // Fallback category if not found
            const fallbackCategory = {
              title: resource.category.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
              icon: BookOpen,
              color: 'text-gray-600 dark:text-gray-400'
            };

            const categoryData = category || fallbackCategory;
            const CategoryIcon = categoryData.icon;

            return (
              <div
                key={resource.id}
                className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-start gap-3 mb-3">
                  <ResourceIcon className="h-5 w-5 text-gray-500 dark:text-gray-400 mt-1 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <CategoryIcon className={`h-4 w-4 ${categoryData.color}`} />
                      <span className={`text-xs font-medium ${categoryData.color}`}>
                        {categoryData.title}
                      </span>
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
                      {resource.title}
                    </h3>
                    {resource.author && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                        by {resource.author} • {resource.duration}
                        {resource.skillLevel && (
                          <span className="ml-2 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                            {resource.skillLevel}
                          </span>
                        )}
                        {resource.cost && resource.cost !== 'free' && (
                          <span className="ml-2 px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded text-xs">
                            {resource.cost}
                          </span>
                        )}
                      </p>
                    )}
                  </div>
                </div>

                <p className="text-gray-700 dark:text-gray-300 mb-4 text-sm leading-relaxed">
                  {resource.description}
                </p>

                {/* Rating Display */}
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex items-center">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-3 w-3 ${
                          star <= (resource.averageRating || 0)
                            ? 'text-yellow-500 fill-current'
                            : 'text-gray-300 dark:text-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {resource.averageRating && resource.totalRatings ? (
                      `${resource.averageRating.toFixed(1)} (${resource.totalRatings} ${resource.totalRatings === 1 ? 'review' : 'reviews'})`
                    ) : (
                      'No ratings yet'
                    )}
                  </span>
                </div>

                <div className="flex gap-2">
                  <Button
                    asChild
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Link
                      href={`/resources/${resource.id}`}
                      className="flex items-center justify-center gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      View Details
                    </Link>
                  </Button>

                  <Button
                    asChild
                    size="sm"
                    className="flex-1"
                  >
                    <a
                      href={resource.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center gap-2"
                    >
                      <span className="capitalize">
                        {resource.type === 'article' ? 'Read' :
                         resource.type === 'video' ? 'Watch' :
                         resource.type === 'podcast' ? 'Listen' :
                         resource.type === 'course' ? 'Take Course' :
                         resource.type === 'certification' ? 'Get Certified' :
                         resource.type === 'tutorial' ? 'Follow Tutorial' : 'View'}
                      </span>
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="mt-12 text-center bg-gray-50 dark:bg-gray-900/20 p-8 rounded-lg">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Ready to Start Your Learning Journey?
        </h3>
        <p className="text-gray-700 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
          Take our assessment to discover which career paths align with your goals, then explore the relevant
          learning resources to build the skills you need for your transition.
        </p>
        <div className="flex gap-4 justify-center flex-wrap">
          <Button asChild>
            <Link href="/assessment">Take Assessment</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/career-paths">Explore Career Paths</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/forum">Join Community</Link>
          </Button>
        </div>
      </div>
    </PageLayout>
  );
}
