'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, ExternalLink, BookOpen, Video, Headphones, Shield, Code, Clock, Star, Bookmark, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ResourceRating from '@/components/resources/ResourceRating';

interface Resource {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  skillLevel: string;
  author?: string;
  duration?: string;
  cost: string;
  averageRating: number;
  totalRatings: number;
  careerPaths: {
    id: string;
    name: string;
    slug: string;
  }[];
}

const getResourceIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'article':
      return BookOpen;
    case 'video':
      return Video;
    case 'podcast':
      return Headphones;
    case 'course':
      return BookOpen;
    case 'certification':
      return Shield;
    case 'tutorial':
      return Code;
    default:
      return BookOpen;
  }
};

const getCostBadgeColor = (cost: string | undefined | null) => {
  if (!cost) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';

  switch (cost.toLowerCase()) {
    case 'free':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'freemium':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    case 'paid':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

const getSkillLevelColor = (level: string | undefined | null) => {
  if (!level) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';

  switch (level.toLowerCase()) {
    case 'beginner':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'intermediate':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'advanced':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

export default function ResourceDetailPage({ params }: { params: Promise<{ id: string }> }) {
  // const router = useRouter();
  const [resource, setResource] = useState<Resource | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bookmarked, setBookmarked] = useState(false);

  useEffect(() => {
    const loadResource = async () => {
      const resolvedParams = await params;
      if (resolvedParams.id) {
        fetchResource(resolvedParams.id);
      }
    };
    loadResource();
  }, [params]);

  const fetchResource = async (id: string) => {
    try {
      setLoading(true);

      // First try to fetch from database API
      const response = await fetch(`/api/learning-resources/${id}`);

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setResource(result.data);
          checkBookmarkStatus(id);
          return;
        }
      }

      // If not found in database, try to find in static resources
      const staticResource = await fetchStaticResource(id);
      if (staticResource) {
        setResource(staticResource);
        checkBookmarkStatus(id);
        return;
      }

      throw new Error('Resource not found');
    } catch (error) {
      console.error('Error fetching resource:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch resource');
    } finally {
      setLoading(false);
    }
  };

  const fetchStaticResource = async (id: string): Promise<Resource | null> => {
    // Import static resources (we'll need to make them available)
    const { getStaticResourceById } = await import('@/lib/staticResources');
    return getStaticResourceById(id);
  };

  const checkBookmarkStatus = async (resourceId: string) => {
    try {
      const response = await fetch(`/api/learning-progress?resourceId=${resourceId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setBookmarked(result.data.status === 'BOOKMARKED');
        }
      }
    } catch (error) {
      console.error('Error checking bookmark status:', error);
    }
  };

  const handleBookmark = async () => {
    if (!resource) return;

    try {
      const response = await fetch('/api/learning-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resourceId: resource.id,
          status: bookmarked ? 'NOT_STARTED' : 'BOOKMARKED',
        }),
      });

      if (response.ok) {
        setBookmarked(!bookmarked);
      } else {
        const errorData = await response.json();
        console.error('Bookmark error:', errorData);
        if (response.status === 401) {
          alert('Please sign in to bookmark resources');
        } else {
          alert('Failed to update bookmark. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error updating bookmark:', error);
      alert('Failed to update bookmark. Please try again.');
    }
  };

  const handleStartResource = () => {
    if (resource) {
      window.open(resource.url, '_blank', 'noopener,noreferrer');
      // Mark as in progress
      fetch('/api/learning-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resourceId: resource.id,
          status: 'IN_PROGRESS',
        }),
      }).catch(console.error);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading resource...</p>
        </div>
      </div>
    );
  }

  if (error || !resource) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Resource Not Found
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {error || 'The resource you\'re looking for doesn\'t exist.'}
          </p>
          <Button asChild>
            <Link href="/resources">Back to Resources</Link>
          </Button>
        </div>
      </div>
    );
  }

  const ResourceIcon = getResourceIcon(resource.type);

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Back Navigation */}
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/resources" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Resources
          </Link>
        </Button>
      </div>

      {/* Resource Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 mb-8">
        <div className="flex items-start gap-6">
          <div className="flex-shrink-0">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <ResourceIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          
          <div className="flex-1">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  {resource.title}
                </h1>
                {resource.author && (
                  <p className="text-lg text-gray-600 dark:text-gray-400">
                    by {resource.author}
                  </p>
                )}
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBookmark}
                  className={bookmarked ? 'bg-yellow-50 border-yellow-200 text-yellow-700' : ''}
                >
                  <Bookmark className={`h-4 w-4 mr-2 ${bookmarked ? 'fill-current' : ''}`} />
                  {bookmarked ? 'Bookmarked' : 'Bookmark'}
                </Button>
                
                <Button onClick={handleStartResource} className="flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  Start Learning
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Metadata */}
            <div className="flex flex-wrap gap-3 mb-4">
              {resource.cost && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCostBadgeColor(resource.cost)}`}>
                  {resource.cost.charAt(0).toUpperCase() + resource.cost.slice(1)}
                </span>
              )}

              {resource.skillLevel && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getSkillLevelColor(resource.skillLevel)}`}>
                  {resource.skillLevel.charAt(0).toUpperCase() + resource.skillLevel.slice(1)}
                </span>
              )}

              {resource.type && (
                <span className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm font-medium">
                  {resource.type.charAt(0).toUpperCase() + resource.type.slice(1)}
                </span>
              )}
              
              {resource.duration && (
                <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {resource.duration}
                </span>
              )}
            </div>

            {/* Rating Display */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-4 w-4 ${
                        star <= resource.averageRating
                          ? 'text-yellow-500 fill-current'
                          : 'text-gray-300 dark:text-gray-600'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {resource.averageRating.toFixed(1)}
                </span>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  ({resource.totalRatings} {resource.totalRatings === 1 ? 'review' : 'reviews'})
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Resource Description */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          About This Resource
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {resource.description}
        </p>
      </div>

      {/* Career Paths */}
      {resource.careerPaths && resource.careerPaths.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Related Career Paths
          </h2>
          <div className="flex flex-wrap gap-3">
            {resource.careerPaths.map((path) => (
              <Link
                key={path.id}
                href={`/career-paths/${path.slug}`}
                className="px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
              >
                {path.name}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Rating Component */}
      <ResourceRating resourceId={resource.id} resourceTitle={resource.title} />
    </div>
  );
}
